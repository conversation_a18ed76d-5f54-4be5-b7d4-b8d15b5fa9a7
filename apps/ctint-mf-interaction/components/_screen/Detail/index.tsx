/* eslint-disable react/no-unescaped-entities */
'use client';
import React, {
  startTransition,
  useState,
  useEffect,
  useCallback,
} from 'react';
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@cdss-modules/design-system/components/_ui/Resizable';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import AuthChecker from '@cdss-modules/design-system/components/_ui/AuthChecker';
import Panel from '@cdss-modules/design-system/components/_ui/Panel';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import AudioPlayer from '@cdss-modules/design-system/components/_ui/AudioPlayer';
import {
  useRole,
  useRouteHandler,
  QMProvider,
  EvaluationFormListProvider,
  useQM,
  SortingButton,
} from '@cdss-modules/design-system';
import Breadcrumb from '@cdss-modules/design-system/components/_ui/Breadcrumb';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import dayjs from 'dayjs';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import { AudioLoadOptions, useGlobalAudioPlayer } from 'react-use-audio-player';
import {
  camelCaseToWords,
  cn,
  secondsToTimeDisplay,
  timeStringToSeconds,
} from '@cdss-modules/design-system/lib/utils';
import {
  fireGetRecordingMedia,
  fireGetSingleRecording,
  fireGetTranscript,
  fireGetEvaluationFormTemplate,
  fireGetQmEvaluationList,
  fireGetStandScriptResult,
  fireGetSingleRecordingStream,
  fireGetEvaluationResult,
  fireGetMetaData,
} from '../../../lib/api';

import {
  Tabs,
  TabsContent,
  Trigger,
} from '@cdss-modules/design-system/components/_ui/Tabs';
import QMPanel from '../../_ui/QMPanel';
import AssignEvaluationPopup from '../../_ui/AssignEvaluationPopup';
import { Wrapup, Wrapups } from '../../../types/wrapups';
import { TParticipants } from '../../../types/participants';
import StorageBox from '@cdss-modules/design-system/components/_ui/StorageBox';
import IconType from '@cdss-modules/design-system/components/_ui/Icon/IconType';
import IconStartTime from '@cdss-modules/design-system/components/_ui/Icon/IconStartTime';
import IconEndTime from '@cdss-modules/design-system/components/_ui/Icon/IconEndTime';
import IconInteractionId from '@cdss-modules/design-system/components/_ui/Icon/IconInteractionId';
import IconDirection from '@cdss-modules/design-system/components/_ui/Icon/IconDirection';
import IconDuration from '@cdss-modules/design-system/components/_ui/Icon/IconDuration';
import IconQueue from '@cdss-modules/design-system/components/_ui/Icon/IconQueue';
import IconUsers from '@cdss-modules/design-system/components/_ui/Icon/IconUsers';
import WrapUp from '../../_ui/WrapUp';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import { RootObject, TTranscriptStatus } from '../../../types/transcript';
import { QmEvaluationListItem, QmFormList } from '../../../types/autoqm';
import PDFViewer from '@cdss-modules/design-system/components/_ui/PDFViewer';
import { DUMMY_DEFAULT_ANSWERS } from '../../../lib/dummy/qa';
import { QmVisitPermission } from '@cdss-modules/design-system/@types/QmVisitPermission';
import { mfName } from '../../../lib/appConfig/index';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { TranscriptList } from '../../_ui/TranscriptList';
import { RefreshCw } from 'lucide-react';
import InputSearch from '@cdss-modules/design-system/components/_ui/InputSearch';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import { MetaData } from '../../../types/metadata';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { ICriteria } from '@cdss-modules/design-system/@types/config';
import { TOrderByInput } from '../Main';
import { Table as TableType } from '@tanstack/react-table';

// TODO: consider move to global helper
const getFilteredTranscripts = (transcripts?: any[]) => {
  if (!Array.isArray(transcripts)) return null;
  return transcripts?.filter((transcript) => {
    return transcript?.speaker !== 'speaker';
  });
};
const getSpeakers = (transcripts?: any[]) => {
  if (!Array.isArray(transcripts)) return null;
  return transcripts?.reduce((acc, curr) => {
    const speaker = curr?.speaker;
    if (!acc.includes(speaker) && speaker !== 'speaker') {
      acc.push(speaker);
    }
    return acc;
  }, []);
};

export const TableDemoDetailBody = () => {
  const { toPath, searchParams, basePath } = useRouteHandler();
  const { t, i18n } = useTranslation();
  const { globalConfig } = useRole();
  const queryClient = useQueryClient();
  const [transcriptLoading, setTranscriptLoading] = useState(false);
  const [transcript, setTranscript] = useState();
  const [currentRecordingId, setCurrentRecordingId] = useState('');
  const [audioUrl, setAudioUrl] = useState('');
  // const isUAT = apiConfig.isUAT;
  const id = searchParams?.get('id') || '';
  const tab = searchParams?.get('tab') || 'info';
  const qaId = searchParams?.get('qaId') || '';
  const [defaultColumns, setDefaultColumns] = useState<ICriteria[]>([]);
  const [sortOrder, setSortOrder] = useState<TOrderByInput>();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(10);

  useEffect(() => {
    setDefaultColumns(
      globalConfig?.microfrontends?.['ctint-mf-interaction']
        ?.metaDataMappingColumns || []
    );
  }, []);

  const {
    showStrandScript,
    openedQA,
    formId,
    standardScriptDirection,
    toggleDirection,
    currentPosition,
    updateCurrentPosition,
  } = useQM();

  const { permissions } = usePermission();

  // call the assign evaluation form template API, method: GET
  const { data: evaluationFormData, isLoading: isLoadingEvaluationForm } =
    useQuery({
      queryKey: ['evaluationForm', currentRecordingId],
      queryFn: async () =>
        await fireGetEvaluationFormTemplate(basePath).then((res) => res.data),
      enabled: !!currentRecordingId,
    });

  // call the conversation detail info API, method: GET
  const { data, isLoading, error } = useQuery({
    queryKey: ['recordings', id],
    queryFn: async () =>
      await fireGetSingleRecording(id, basePath).then((res) => res.data),
    enabled: !!id,
  });

  const { data: qmEvaluationList } = useQuery({
    queryKey: ['qmEvaluationList', currentRecordingId],
    queryFn: async () =>
      await fireGetQmEvaluationList(currentRecordingId, basePath).then(
        (res) => res.data
      ),
    enabled: !!currentRecordingId,
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });

  // query evaluation result
  const { data: evaluationResult, isPending: isLoadingEvaluationResult } =
    useQuery({
      queryKey: ['evaluationResult', openedQA],
      queryFn: async () => {
        const res = await fireGetEvaluationResult(
          openedQA,
          currentRecordingId,
          basePath
        );
        return res?.data ? res.data : { data: [] };
      },
      enabled: !!openedQA,
      refetchInterval: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    });

  const details = data?.data;

  const hasTranscript = globalConfig?.services?.['ctint-stt']?.active;

  const qmVisitPermission = new QmVisitPermission(globalConfig, permissions);

  const callDirection = details?.direction?.toLowerCase() ?? 'inbound';

  // useEffect(() => {
  //   if (details?.transcript) {
  //     setTranscript(details?.transcript);
  //   }
  // }, [details]);

  const speakers = getSpeakers(transcript);

  const { load, isReady, seek, error: loadMp3Error } = useGlobalAudioPlayer();

  const [pos, setPos] = useState(0);

  const { data: mp3Data, isPending: isLoadingMp3 } = useQuery({
    queryKey: ['mediaUris', id],
    queryFn: async () => {
      const result = await fireGetRecordingMedia(id, basePath).then(
        (res) => res.data
      );
      return result;
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  });

  const { data: transcriptData, isPending: isLoadingTranscript } = useQuery({
    queryKey: ['transcript', id],
    queryFn: async () => {
      const result = await fireGetTranscript(id, basePath).then(
        (res) => res.data
      );
      return result;
    },
    enabled: !!id,
    refetchInterval: 1000 * 60 * 5, // 5 minutes
  });

  // query strand script result by formId
  const { data: standScriptResult, isPending: isLoadingStandScriptResult } =
    useQuery({
      queryKey: ['standScriptResult', openedQA],
      queryFn: async () => {
        const res = await fireGetStandScriptResult(formId, basePath);
        return res?.data ? res.data : { data: [] };
      },
      enabled: !!openedQA,
    });

  const {
    data: metaDataList,
    isPending: isLoadingMetaData,
    error: errorMetaData,
  } = useQuery({
    queryKey: ['metaData', currentRecordingId],
    queryFn: async () => {
      const res = await fireGetMetaData(
        { recordingId: currentRecordingId, page: 1, pageSize: 10 },
        basePath
      );
      return res?.data ? res.data : { data: [] };
    },
    enabled: !!currentRecordingId,
  });

  // FOR STREAM
  // const recordingId = mp3Data?.data ? mp3Data?.data[0]?.id : null;

  // FOR STREAM
  // useEffect(() => {
  //   if (recordingId) {
  //     console.log('recordingId', recordingId);
  //     fireGetSingleRecordingStream(recordingId, basePath).then((res) => {
  //       console.log('fireGetSingleRecordingStream ===> res', res);
  //       if (res.status === 200) {
  //         const audioBlob = new Blob([res.data], { type: 'audio/mpeg' });
  //         const audioUrl = URL.createObjectURL(audioBlob);
  //         console.log('audioUrl', audioUrl);
  //         setAudioUrl(audioUrl);
  //       }
  //     });
  //   }
  // }, [recordingId, basePath]);
  // 默认拿第一个 recording
  const mp3FileUrl = mp3Data?.data ? mp3Data?.data[0]?.mediaUri : null;
  // const mp3FileUrl = audioUrl; // FOR STREAM

  useEffect(() => {
    if (mp3Data?.data && mp3Data?.data[0]?.id) {
      setCurrentRecordingId(mp3Data?.data[0]?.id);
    }
  }, [mp3Data]);

  const loadMp3 = useCallback(() => {
    if (!mp3FileUrl) return;
    const loadOptions: AudioLoadOptions = { initialVolume: 0.5 };
    loadOptions.html5 = true;
    loadOptions.format = 'mp3';

    load(mp3FileUrl, loadOptions);
  }, [load, mp3FileUrl]);

  useEffect(() => {
    if (!mp3FileUrl) return;
    startTransition(() => {
      const loadOptions: AudioLoadOptions = { initialVolume: 0.5 };
      loadOptions.html5 = true;
      loadOptions.format = 'mp3';

      load(mp3FileUrl, loadOptions);
    });
  }, [mp3FileUrl, load, loadMp3]);

  useEffect(() => {
    
  }, [perPage]);

  /**
   * AudioPlayer Log Start
   */
  useEffect(() => {
    console.log('AudioPlayer Log => isReady', isReady);
  }, [isReady]);

  useEffect(() => {
    console.log('AudioPlayer Log => error', loadMp3Error);
  }, [loadMp3Error]);

  useEffect(() => {
    console.log('AudioPlayer Log => mp3FileUrl', mp3FileUrl);
  }, [mp3FileUrl]);

  useEffect(() => {
    console.log('AudioPlayer Log => isLoadingMp3', isLoadingMp3);
  }, [isLoadingMp3]);
  /**
   * AudioPlayer Log End
   */

  const getLettersFromUsername = (username: string, length = 2) => {
    let output = '';
    for (let i = 0; i < length; i++) {
      const letter = username?.[i];
      if (letter) {
        output += letter.toUpperCase();
      }
    }
    return output || 'N/A';
  };

  // const lastSpeaker = '';

  // QA / Evaluations
  const [assignEvaluationPopupOpen, setAssignEvaluationPopupOpen] =
    useState(false);
  const [reAssignEvaluationPopupOpen, setReAssignEvaluationPopupOpen] =
    useState(false);
  const openAssignEvaluationPopup = () => {
    setAssignEvaluationPopupOpen(true);
  };
  const openReAssignEvaluationPopup = () => {
    setReAssignEvaluationPopupOpen(true);
  };

  /**
   * Render wrapups as a list of WrapUp components.
   * @param {Wrapups} wrapups A dictionary of wrapup names to lists of wrapup items.
   * @returns A list of WrapUp components.
   *
   * If wrapups is an empty object or null, return a message indicating that the wrapups are not available.
   * Otherwise, render a list of WrapUp components, where each component represents a wrapup and contains a list of wrapup items.
   */
  const renderWrapups = (wrapups: Wrapups) => {
    if (!wrapups || Object.keys(wrapups).length == 0 || !wrapups?.wrapList)
      return (
        <div className="truncate text-[#636363]">
          {t('ctint-mf-interaction.details.notAvailable')}
        </div>
      );

    if (wrapups?.wrapList) {
      return wrapups?.wrapList?.map((wrapup: Wrapup[], index: number) => {
        return (
          <WrapUp
            key={index}
            wrapup={wrapup}
          />
        );
      });
    }
  };

  const renderParticipants = (participants: TParticipants) => {
    if (!participants || Object.keys(participants).length == 0)
      return (
        <div className="truncate text-[#636363]">
          {t('ctint-mf-interaction.details.notAvailable')}
        </div>
      );
    return Object.keys(participants).map((key: string) => {
      return (
        <StorageBox
          key={key}
          title={key}
          data={participants[key] ? participants[key] : { name: 'N/A' }}
        />
      );
    });
  };

  // 定义一个映射函数，将字符串状态映射到允许的状态
  const mapStatusToAllowedValues = (
    status: string
  ): 'published' | 'completed' | 'inprogress' | 'init' | 'failed' => {
    switch (status) {
      case 'published':
      case 'completed':
      case 'inprogress':
      case 'init':
      case 'failed':
        return status;
      default:
        return 'failed'; // 或者选择一个默认值
    }
  };

  type TTranscript = RootObject;
  type TTranscriptList = TTranscript[];
  const renderTranscript = (transcript: TTranscript) => {
    if (!transcript || transcript?.transcripts?.length === 0) return null;

    // when the transcript in progressing
    if (
      transcript?.status == TTranscriptStatus.IN_PROGRESS ||
      transcript?.status == TTranscriptStatus.INIT
    )
      return (
        <div className="w-full h-full flex flex-col items-center justify-center">
          <IconEmptyRecords size="78" />
          <div className="text-grey-500">Transcript in progressing ....</div>
        </div>
        // <div className="text-center h-full">
        //   <div className="w-full h-full flex flex-col items-center justify-center py-12">
        //     <Loader size={64} />
        //     <p className="text-body mt-4">
        //       {t('ctint-mf-interaction.transcript.processing')}
        //     </p>
        //   </div>
        // </div>
      );

    // when the transcript ready
    if (transcript?.status == TTranscriptStatus.COMPLETED)
      return (
        <TranscriptList
          transcript={transcript}
          nlpResult={evaluationResult?.data}
        />
      );

    // when the transcript fail
    if (transcript?.status == TTranscriptStatus.FAILED)
      return (
        <div className="w-full h-full flex flex-col items-center justify-center">
          <IconEmptyRecords size="78" />
          <div className="text-grey-500">Transcript Failed.</div>
        </div>
      );
  };

  // 20241022 - For now hard coded standard script PDF (beacause initally first client CCB has only one standard script)
  // TODO: should be dynamic based on the formId when backend ready

  const port = window?.location?.port ? `:${window.location.port}` : '';
  const standardScriptPdf = `${window?.location?.protocol}//${window?.location?.hostname}${port}${basePath}/images/standard-script-eli.pdf`;

  const generateMetaDataMappingColumns = (
    columns: ICriteria[],
    sortOrder: TOrderByInput | undefined,
    setSortOrder: (input: TOrderByInput) => void,
    basePath = '',
    t: any,
    actions: any,
    i18n: any
  ) => {
    const metaDataCol = columns.map((column: any) => {
      return {
        id: column.value,
        accessorKey: column.value,
        header: () => {
          return (
            <SortingButton
              sorting={
                sortOrder?.[column.value]
                  ? sortOrder?.[column.value] === 'asc'
                    ? 'asc'
                    : 'desc'
                  : false
              }
              onClick={async () => {
                const targetSortOrder =
                  sortOrder?.[column.value] === 'asc' ? 'desc' : 'asc';
                setSortOrder({
                  [column.value]: targetSortOrder,
                });
              }}
            >
              {i18n.language === 'en' ? column.labelEn : column.labelCh}
            </SortingButton>
          );
        },
        cell: ({ row }) => {
          const val = row.getValue(column.value) as any;

          return <div>{val ? val : '--'}</div>;
        },
      } as ColumnDef<MetaData>;
    });

    return [...metaDataCol];
  };

  const getTabs = () => {
    const tabs: Trigger[] = [
      // Info, default
      {
        value: 'info',
        label: t('ctint-mf-interaction.tabs.info'),
      },
    ];

    const commonPermission = new CommonPermission(globalConfig, permissions);

    // QM
    if (commonPermission.isPermissionEnabled(mfName, 'qm', 'visit')) {
      tabs.push({
        value: 'qa',
        label: t('ctint-mf-interaction.tabs.qa'),
      });
    }

    // MetaData Mapping
    if (commonPermission.isPermissionEnabled(mfName, 'qm-metadata', 'visit')) {
      tabs.push({
        value: 'meta_data_mapping',
        label: t('ctint-mf-interaction.tabs.metaDataMapping'),
      });
    }

    return tabs;
  };

  // Pagination
  const totalCount = metaDataList?.data?.totalCount ?? data?.totalCount ?? 0;
  const totalPages = Math.ceil(totalCount / perPage);

  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  return (
    <div className="relative flex flex-col h-full">
      <div className="mb-4 flex items-center gap-2">
        <Button
          asSquare
          variant="back"
          onClick={() => toPath(`/`)}
          beforeIcon={<Icon name="back" />}
        />
        <Breadcrumb
          items={[
            {
              label: t('ctint-mf-interaction.filter.search'),
              link: '/',
            },
            {
              label: error || !id ? 'Error' : `${id}`,
              link: '',
            },
          ]}
        />
      </div>
      {error || !id ? (
        <Panel containerClassName="h-full">
          <div className="p-4">
            <h2 className="mb-2 text-t6 font-bold">{`Error loading data: ${
              id ? error?.message : 'No valid ID.'
            }`}</h2>
          </div>
        </Panel>
      ) : (
        <>
          {isLoading ? (
            <Panel
              loading={isLoading}
              containerClassName="h-full"
            />
          ) : (
            <>
              <ResizablePanelGroup
                autoSaveId="playback-detail-panel"
                direction="horizontal"
                className="flex-1 flex w-full h-0 gap-x-3 overflow-auto"
              >
                <ResizablePanel minSize={30}>
                  <Panel
                    loading={isLoading}
                    containerClassName="h-full"
                  >
                    <Tabs
                      defaultTab={tab}
                      triggers={getTabs()}
                      triggerClassName="py-1 px-2 text-body"
                    >
                      <TabsContent
                        value={'info'}
                        className="overflow-y-auto p-0"
                      >
                        {/* Interaction Metrics */}
                        <section>
                          <div>
                            <h2 className="p-4 text-t6 font-bold text-other-orange">
                              {t('ctint-mf-interaction.details.metrics')}
                            </h2>
                          </div>
                          <div className="px-4 grid grid-cols-2 gap-6">
                            {/* Conversation Type */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconType
                                  alt="Media Type"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.mediaType'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {camelCaseToWords(
                                      details?.conversationType
                                    ) || 'N/A'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Start Time */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconStartTime
                                  alt="Start Time"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationStart'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.conversationStart
                                      ? dayjs(
                                          details?.conversationStart
                                        ).format(GLOBAL_DATETIME_FORMAT)
                                      : 'N/A'}{' '}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* End Time */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconEndTime
                                  alt="End Time"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationEnd'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.conversationEnd
                                      ? dayjs(details?.conversationEnd).format(
                                          GLOBAL_DATETIME_FORMAT
                                        )
                                      : 'N/A'}{' '}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Conversation ID */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconInteractionId
                                  alt="Interaction Id"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t('ctint-mf-interaction.columns.id')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.conversationId || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Initiator */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconDirection
                                  alt="Direction"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.direction'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.originatingDirection || 'N/A'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Duration */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconDuration
                                  alt="Duration"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-2 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.columns.conversationDuration'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {secondsToTimeDisplay(details?.duration, [
                                      t('ctint-mf-interaction.details.hours'),
                                      t('ctint-mf-interaction.details.minutes'),
                                      t('ctint-mf-interaction.details.seconds'),
                                    ]) || 'N/A'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* Queue */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconQueue
                                  alt="Queue"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-4 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t('ctint-mf-interaction.columns.queues')}:
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>{details?.queue || 'N/A'}</p>
                                </div>
                              </section>
                            </section>

                            {/* Internal Participant(s) */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconUsers
                                  alt="Internal Participant"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-4 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.details.internalParticipantTitle'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.internalName &&
                                    details?.internalName.length > 0
                                      ? details?.internalName
                                      : '--'}
                                  </p>
                                </div>
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.details.disconnectReasonTitle'
                                    )}
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.internalDisconnectReason &&
                                    details?.internalDisconnectReason.length > 0
                                      ? details?.internalDisconnectReason
                                      : '--'}
                                  </p>
                                </div>
                              </section>
                            </section>

                            {/* External Participant(s) */}
                            <section className="flex flex-row flex-wrap gap-2">
                              <div className="row-span-2">
                                <IconUsers
                                  alt="Internal Participant"
                                  size="24"
                                  color="black"
                                />
                              </div>
                              <section className="grid grid-rows-4 grid-flow-col gap-2">
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.details.externalParticipantTitle'
                                    )}
                                    :
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.externalName &&
                                    details?.externalName.length > 0
                                      ? details?.externalName
                                      : '--'}
                                  </p>
                                </div>
                                <div className="row-span-1 col-span-11">
                                  <strong>
                                    {t(
                                      'ctint-mf-interaction.details.disconnectReasonTitle'
                                    )}
                                  </strong>
                                </div>
                                <div className="row-span-1 col-span-11 font-light text-[#636363]">
                                  <p>
                                    {details?.externalDisconnectType &&
                                    details?.externalDisconnectType.length > 0
                                      ? details?.externalDisconnectType
                                      : '--'}
                                  </p>
                                </div>
                              </section>
                            </section>
                          </div>
                        </section>

                        {/* Wrap-ups */}
                        <section>
                          <div>
                            <h2 className="p-4 text-t6 font-bold text-other-orange">
                              {t('ctint-mf-interaction.details.wrapups')}
                            </h2>
                          </div>
                          <section className="px-4">
                            {/* {renderWrapups(details?.wrapups || {})} */}
                            {renderWrapups(details?.wrapups || {})}
                            {/* render Wrapups remark */}
                            <p className="px-3">
                              {details?.wrapups?.remark
                                ? details?.wrapups?.remark
                                : ''}
                            </p>
                          </section>
                        </section>

                        {/* Participant Data */}
                        <section>
                          <div>
                            <h2 className="p-4 text-t6 font-bold text-other-orange">
                              {t('ctint-mf-interaction.details.participant')}
                            </h2>
                          </div>
                          <section className="px-4">
                            {renderParticipants(details?.attributes || {})}
                          </section>
                        </section>
                      </TabsContent>
                      <TabsContent
                        value={'qa'}
                        className="overflow-y-auto p-0 h-full"
                      >
                        <QMPanel
                          defaultOpenedQA={qaId}
                          currentRecordingId={currentRecordingId}
                          onAssign={openAssignEvaluationPopup}
                          onReassign={openReAssignEvaluationPopup}
                          data={
                            qmEvaluationList?.data.map(
                              (item: QmEvaluationListItem) => ({
                                ...item,
                                status: mapStatusToAllowedValues(item.status),
                              })
                            ) || []
                          }
                        />
                      </TabsContent>
                      <TabsContent
                        value={'meta_data_mapping'}
                        className="flex flex-col p-2 h-full"
                      >
                        <div className="flex-1 overflow-auto px-4 pt-4 w-full">
                          <DataTable<MetaData>
                            data={
                              isLoadingMetaData
                                ? []
                                : metaDataList?.data?.metadataList || []
                            }
                            columns={generateMetaDataMappingColumns(
                              defaultColumns,
                              sortOrder,
                              (input) => {
                                setSortOrder(input);
                              },
                              basePath,
                              t,
                              null,
                              i18n
                            )}
                            loading={isLoadingMetaData}
                            error={errorMetaData?.message}
                            resize
                          />
                        </div>
                        <div>
                          {totalPages > 0 && (
                            <section className="flex-1 flex-row">
                              <div>
                                <Pagination
                                  current={currentPage}
                                  perPage={perPage}
                                  total={totalPages}
                                  totalCount={totalCount}
                                  onChange={(v) => setCurrentPage(v)}
                                  handleOnPrevious={() => handlePrevious()}
                                  handleOnNext={() => handleNext()}
                                  handlePerPageSetter={(p: number) => {
                                    setPerPage(p);
                                    setCurrentPage(1);
                                  }}
                                />
                              </div>
                            </section>
                          )}
                        </div>
                      </TabsContent>
                    </Tabs>
                  </Panel>
                </ResizablePanel>
                <ResizableHandle />
                <ResizablePanel
                  minSize={30}
                  className="h-full"
                >
                  <Panel
                    loading={isLoadingMp3}
                    containerClassName="h-full"
                  >
                    <ResizablePanelGroup
                      direction="vertical"
                      className="flex flex-col w-full"
                    >
                      <ResizablePanel>
                        <div className="h-full flex flex-col px-4 py-1">
                          <div className="flex justify-between w-full">
                            <h2 className="text-t6">
                              {t('ctint-mf-interaction.details.recordings')}
                            </h2>
                            <Button
                              className="shrink-0"
                              onClick={() => {
                                // update stand script result
                                queryClient.invalidateQueries({
                                  queryKey: ['transcript', id],
                                });
                              }}
                              variant="blank"
                            >
                              <RefreshCw size={14} />
                              <span className="active:text-other-orange">
                                {t('ctint-mf-interaction.details.refresh')}
                              </span>
                            </Button>
                            <AuthChecker
                              // requiredPemissions={{
                              //   global: {
                              //     portals: ['ctint-mf-interaction'],
                              //   },
                              //   user: {
                              //     permissions: [
                              //       'ctint-mf-interaction.recording.download',
                              //     ],
                              //   },
                              // }}
                              emptyWhenUnauthorized
                            >
                              {/* {loadMp3Error && (
                                <a
                                  href={mp3FileUrl}
                                  download={`${id}.mp3`}
                                >
                                  <Button
                                    size="s"
                                    variant="secondary"
                                  >
                                    {t('ctint-mf-interaction.filter.download')}:
                                  </Button>
                                </a>
                              )} */}
                              <></>
                            </AuthChecker>
                          </div>
                          {/* <div>
                            <InputSearch
                              value={''}
                              placeholder="Search..."
                              options={[
                                {
                                  labelEn: 'Call',
                                  labelCh: 'Call',
                                  value: 'Call',
                                },
                                {
                                  labelEn: 'Chat',
                                  labelCh: 'Chat',
                                  value: 'Chat',
                                },
                                {
                                  labelEn: 'Email',
                                  labelCh: 'Email',
                                  value: 'Email',
                                },
                              ]}
                              onItemSelected={(value) => {
                                console.log(value);
                              }}
                            />
                          </div> */}
                          {/* remove loading */}
                          {/* {!isReady && (
                            <div className="w-full">
                              <p className="mb-4">
                                {`${t('ctint-mf-interaction.details.loading')}`}
                              </p>
                            </div>
                          )} */}
                          {loadMp3Error &&
                            details?.conversationType.includes('Call') && (
                              <div className="w-full">
                                <p className="mb-4">
                                  {`${t('ctint-mf-interaction.filter.recordingError')}(${loadMp3Error})`}
                                </p>
                                <Button onClick={() => loadMp3()}>
                                  {t('ctint-mf-interaction.filter.reload')}
                                </Button>
                              </div>
                            )}
                          {loadMp3Error &&
                            !details?.conversationType.includes('Call') && (
                              <div className="w-full pt-4 border-t border-grey-200">
                                <p className="mb-4">
                                  {`${t('ctint-mf-interaction.details.notAvailable')}`}
                                </p>
                              </div>
                            )}
                          {!isLoadingMp3 && mp3FileUrl ? (
                            <>
                              <div className="w-full pt-4 border-t border-grey-200">
                                <div className="relative w-full">
                                  <AudioPlayer
                                    updatePos={(p) => updateCurrentPosition(p)}
                                    // seekBarCustomComponent={
                                    //   <QMAudioPlayerActiveSection />
                                    // }
                                    label={{
                                      jumpToTime: t(
                                        'ctint-mf-interaction.audio.jumpToTime'
                                      ),
                                      go: t('ctint-mf-interaction.audio.go'),
                                      speed: t(
                                        'ctint-mf-interaction.audio.speed'
                                      ),
                                      invalidTime: t(
                                        'ctint-mf-interaction.audio.invalidTime'
                                      ),
                                    }}
                                  />
                                </div>
                              </div>
                              {/* <QMSOPStages /> */}
                              <ResizablePanelGroup
                                direction={standardScriptDirection}
                                className="flex flex-col w-full flex-1 h-0"
                              >
                                {/* transcript panel */}
                                <ResizablePanel
                                  defaultSize={showStrandScript ? 50 : 100}
                                >
                                  <div
                                    className={cn(
                                      'flex-1 h-full flex flex-col w-full overflow-y-auto',
                                      !hasTranscript && 'hidden'
                                    )}
                                  >
                                    {transcriptData ? (
                                      <div className="h-full">
                                        {renderTranscript(
                                          transcriptData?.data?.transcriptList?.find(
                                            (item: TTranscript) =>
                                              item.recordingId ===
                                              mp3Data?.data[0]?.id
                                          )
                                        )}
                                      </div>
                                    ) : (
                                      <div className="flex flex-col items-center pt-4 border-t border-grey-200">
                                        <IconEmptyRecords size="78" />
                                        <div className="text-grey-500">
                                          No selected interaction.
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </ResizablePanel>
                                <ResizableHandle className="mx-2" />
                                {/* standard script panel */}
                                {showStrandScript && (
                                  <ResizablePanel
                                    defaultSize={showStrandScript ? 50 : 0}
                                    className="pt-6 pb-2"
                                  >
                                    <div className="relative flex flex-col p-2 h-[100%] border border-[#EEEFF1] bg-[#FCFCFD] rounded-lg">
                                      {/* <IconSwitchDirection
                                        size="20"
                                        className="absolute top-2 right-2 bg-[#FCFCFD] cursor-pointer"
                                        onClick={() => {
                                          toggleDirection && toggleDirection();
                                        }}
                                      /> */}
                                      <article className="flex flex-col gap-2 mt-2">
                                        <PDFViewer pdf={standardScriptPdf} />
                                        {/* {standScriptResult?.data.map(
                                          (
                                            item: StandardScriptItem,
                                            index: number
                                          ) => (
                                            <p
                                              key={item.scenarioId}
                                              className={`text-clip ${index == 0 ? 'text-[#FFAC4A] font-bold' : ''}`}
                                            >
                                              {item.scenarioId}
                                              {item.content}
                                            </p>
                                          )
                                        )} */}
                                      </article>
                                    </div>
                                  </ResizablePanel>
                                )}
                              </ResizablePanelGroup>
                            </>
                          ) : (
                            <>
                              {!loadMp3Error && (
                                <div className="w-full h-full flex flex-col items-center justify-center">
                                  <IconEmptyRecords size="78" />
                                  <div className="text-grey-500">
                                    No selected interaction.
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </ResizablePanel>
                    </ResizablePanelGroup>
                  </Panel>
                </ResizablePanel>
              </ResizablePanelGroup>
              <EvaluationFormListProvider<QmFormList>
                formList={
                  evaluationFormData?.data ? evaluationFormData : { data: [] }
                }
              >
                <AssignEvaluationPopup
                  currentRecordingId={currentRecordingId}
                  selectedIntergrations={[details]}
                  open={assignEvaluationPopupOpen}
                  onOpenChange={(v) => setAssignEvaluationPopupOpen(v)}
                />

                <AssignEvaluationPopup
                  currentRecordingId={currentRecordingId}
                  selectedIntergrations={[details]}
                  open={reAssignEvaluationPopupOpen}
                  onOpenChange={(v) => setReAssignEvaluationPopupOpen(v)}
                />
              </EvaluationFormListProvider>
            </>
          )}
        </>
      )}
    </div>
  );
};
// Create a client
const queryClient = new QueryClient();

export const TableDemoDetail = () => (
  <QueryClientProvider client={queryClient}>
    <QMProvider defaultFormAns={DUMMY_DEFAULT_ANSWERS}>
      <TableDemoDetailBody />
    </QMProvider>
  </QueryClientProvider>
);

export default TableDemoDetail;
